import { FileTypeEnum, MarketplaceMediaTypeEnum } from '@shared/utils';
import { ImageTypeEnum } from '@shared/utils';
import { VideoTypeEnum } from '@shared/utils';
import { AudioTypeEnum } from '@shared/utils';
import { ContentMediaTypeEnum } from '@utils/file/content-media-type.util';



/**
 * Union type cho tất cả các loại media type được hỗ trợ
 * Chỉ hỗ trợ Image, Video và Audio - không hỗ trợ Document files
 */
export type MediaType = ImageTypeEnum | VideoTypeEnum | AudioTypeEnum | MarketplaceMediaTypeEnum | ContentMediaTypeEnum;

/**
 * Object tiện ích để làm việc với tất cả các loại media type
 */
export const MediaTypeUtil = {
  /**
   * Lấy giá trị MIME type từ bất kỳ loại media type nào
   * @param type Loại media (ImageTypeEnum, VideoTypeEnum, AudioTypeEnum)
   * @returns Gi<PERSON> trị MIME type tương ứng
   */
  getValue(type: MediaType): string {
    return type;
  },

  /**
   * Kiểm tra xem một giá trị có phải là ImageTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là ImageTypeEnum, false nếu không phải
   */
  isImageType(type: MediaType): type is ImageTypeEnum {
    return Object.values(ImageTypeEnum).includes(type as ImageTypeEnum);
  },

  /**
   * Kiểm tra xem một giá trị có phải là VideoTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là VideoTypeEnum, false nếu không phải
   */
  isVideoType(type: MediaType): type is VideoTypeEnum {
    return Object.values(VideoTypeEnum).includes(type as VideoTypeEnum);
  },

  /**
   * Kiểm tra xem một giá trị có phải là AudioTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là AudioTypeEnum, false nếu không phải
   */
  isAudioType(type: MediaType): type is AudioTypeEnum {
    return Object.values(AudioTypeEnum).includes(type as AudioTypeEnum);
  },
};
