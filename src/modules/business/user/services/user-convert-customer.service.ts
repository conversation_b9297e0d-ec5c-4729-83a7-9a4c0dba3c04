import { Injectable, Logger } from '@nestjs/common';
import { UserConvertCustomerRepository, CustomerFacebookRepository, CustomerWebRepository } from '@modules/business/repositories';
import { PaginatedResult } from '@common/response';
import { AgentInfoDto, CreateUserConvertCustomerDto, CreateBulkUserConvertCustomerDto, BulkUserConvertCustomerResponseDto, BulkCreateResultItemDto, UpdateUserConvertCustomerDto, MergeUserConvertCustomerDto, QueryUserConvertCustomerDto, UserConvertCustomerListItemDto, UserConvertCustomerResponseDto, UserInfoDto, UpdateCustomerSocialDto, CustomerSocialResponseDto, CustomerFacebookResponseDto, CustomerWebResponseDto, MetadataFieldDto, MetadataFieldResponseDto, BulkDeleteUserConvertCustomerDto, BulkDeleteUserConvertCustomerResponseDto} from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ValidationHelper } from '../helpers/validation.helper';
import { UserConvertCustomer } from '@modules/business/entities';
import { CustomField } from '@modules/business/entities';
import { CustomFieldService } from './custom-field.service';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { ImageType, ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { TimeIntervalEnum, FileSizeEnum } from '@shared/utils';


/**
 * Service xử lý logic nghiệp vụ cho khách hàng chuyển đổi
 */
@Injectable()
export class UserConvertCustomerService {
  private readonly logger = new Logger(UserConvertCustomerService.name);

  constructor(
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly customFieldService: CustomFieldService,
    private readonly customerFacebookRepository: CustomerFacebookRepository,
    private readonly customerWebRepository: CustomerWebRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo khách hàng chuyển đổi mới
   * @param userId ID người dùng
   * @param createDto DTO tạo khách hàng chuyển đổi
   * @returns Khách hàng chuyển đổi đã được tạo
   */
  async create(userId: number, createDto: CreateUserConvertCustomerDto): Promise<UserConvertCustomerResponseDto> {
    try {
      this.logger.log(`Tạo khách hàng chuyển đổi mới cho userId=${userId}, phone=${createDto.phone}`);

      // Kiểm tra số điện thoại đã tồn tại chưa
      const existingCustomer = await this.userConvertCustomerRepository.findByPhone(createDto.phone);
      if (existingCustomer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
          `Số điện thoại ${createDto.phone} đã tồn tại trong hệ thống`
        );
      }

      // Kiểm tra agentId có tồn tại và thuộc về user không (nếu có)
      if (createDto.agentId) {
        const agentExists = await this.userConvertCustomerRepository.checkAgentExists(createDto.agentId, userId);
        if (!agentExists) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
            `Agent với ID ${createDto.agentId} không tồn tại hoặc không thuộc về bạn`
          );
        }
      }

      // Xử lý avatar upload nếu có
      let avatarUrl: string | undefined;
      let avatarUploadInfo: { uploadUrl: string; key: string; expiresAt: number } | undefined;

      if (createDto.avatar) {
        this.logger.log(`Tạo presigned URL cho avatar: ${createDto.avatar.fileName}`);

        // Xác định loại hình ảnh từ MIME type
        let imageType: ImageTypeEnum;
        try {
          imageType = ImageType.getType(createDto.avatar.mimeType);
        } catch (error) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
            `Loại MIME không được hỗ trợ: ${createDto.avatar.mimeType}`
          );
        }

        // Tạo S3 key cho avatar
        const s3Key = generateS3Key({
          baseFolder: 'customers',
          categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
          fileName: createDto.avatar.fileName,
          prefix: `user_${userId}`,
          useTimeFolder: true,
        });

        // Tạo presigned URL
        const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
        const uploadUrl = await this.s3Service.createPresignedWithID(
          s3Key,
          expirationTime,
          imageType,
          FileSizeEnum.FIVE_MB,
        );

        // Tính thời gian hết hạn
        const expiresAt = Date.now() + expirationTime * 1000;

        // Tạo CDN URL để xem avatar sau khi upload
        const viewUrl = this.cdnService.generateUrlView(s3Key, TimeIntervalEnum.THIRTY_DAYS);
        if (viewUrl) {
          avatarUrl = viewUrl;
        }

        avatarUploadInfo = {
          uploadUrl,
          key: s3Key,
          expiresAt,
        };

        this.logger.log(`Đã tạo presigned URL upload avatar với key=${s3Key}`);
      }

      // Xử lý email - chuyển string thành object nếu cần
      const emailData = this.processEmailData(createDto.email);

      // Validate metadata fields if provided
      const metadataToStore = await this.processMetadata(createDto.metadata, createDto.tags, userId);

      // Chuẩn bị dữ liệu để tạo khách hàng
      const customerData: Partial<UserConvertCustomer> = {
        name: createDto.name,
        phone: createDto.phone,
        email: emailData,
        avatar: avatarUrl,
        platform: createDto.platform,
        timezone: createDto.timezone,
        userId: userId,
        agentId: createDto.agentId,
        metadata: metadataToStore,
        facebookLink: createDto.facebookLink,
        twitterLink: createDto.twitterLink,
        linkedinLink: createDto.linkedinLink,
        zaloLink: createDto.zaloLink,
        websiteLink: createDto.websiteLink,
        address: createDto.address || undefined,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      // Tạo khách hàng chuyển đổi
      const customer = await this.userConvertCustomerRepository.createUserConvertCustomer(customerData);

      // Chuyển đổi sang DTO response
      const responseDto = await this.convertToResponseDto(customer, userId);

      // Thêm thông tin upload avatar nếu có
      if (avatarUploadInfo) {
        responseDto.avatarUpload = avatarUploadInfo;
      }

      this.logger.log(`Đã tạo khách hàng chuyển đổi thành công với ID=${customer.id}`);
      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo khách hàng chuyển đổi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
        `Lỗi khi tạo khách hàng chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Tạo nhiều khách hàng chuyển đổi cùng lúc
   * @param userId ID người dùng
   * @param createBulkDto DTO tạo nhiều khách hàng chuyển đổi
   * @returns Kết quả bulk create
   */
  async createBulk(userId: number, createBulkDto: CreateBulkUserConvertCustomerDto): Promise<BulkUserConvertCustomerResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.log(`Bắt đầu tạo bulk ${createBulkDto.customers.length} khách hàng chuyển đổi cho userId=${userId}`);

      const results: BulkCreateResultItemDto[] = [];
      let successCount = 0;
      let errorCount = 0;
      let skippedCount = 0;

      // Kiểm tra trùng lặp số điện thoại trong request
      const phoneMap = new Map<string, number[]>();
      createBulkDto.customers.forEach((customer, index) => {
        if (customer.phone) {
          if (!phoneMap.has(customer.phone)) {
            phoneMap.set(customer.phone, []);
          }
          phoneMap.get(customer.phone)!.push(index);
        }
      });

      // Tìm các số điện thoại trùng lặp trong request
      const duplicatePhones = new Set<string>();
      for (const [phone, indices] of phoneMap.entries()) {
        if (indices.length > 1) {
          duplicatePhones.add(phone);
        }
      }

      // Kiểm tra số điện thoại đã tồn tại trong database
      const allPhones = createBulkDto.customers.map(c => c.phone).filter(Boolean);
      const existingPhones = new Set(await this.userConvertCustomerRepository.findExistingPhones(allPhones));

      // Chuẩn bị danh sách khách hàng hợp lệ để tạo
      const validCustomersData: Array<{ data: Partial<UserConvertCustomer>; index: number }> = [];

      // Xử lý từng khách hàng
      for (let index = 0; index < createBulkDto.customers.length; index++) {
        const customerDto = createBulkDto.customers[index];

        try {
          // Kiểm tra trùng lặp trong request
          if (duplicatePhones.has(customerDto.phone)) {
            const firstIndex = phoneMap.get(customerDto.phone)![0];
            if (index !== firstIndex) {
              if (createBulkDto.skipDuplicates) {
                results.push({
                  index,
                  status: 'skipped',
                  message: `Số điện thoại ${customerDto.phone} bị trùng lặp trong request`,
                  originalData: customerDto,
                });
                skippedCount++;
                continue;
              } else {
                throw new AppException(
                  BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
                  `Số điện thoại ${customerDto.phone} bị trùng lặp trong request`
                );
              }
            }
          }

          // Kiểm tra trùng lặp với database
          if (existingPhones.has(customerDto.phone)) {
            if (createBulkDto.skipDuplicates) {
              results.push({
                index,
                status: 'skipped',
                message: `Số điện thoại ${customerDto.phone} đã tồn tại trong hệ thống`,
                originalData: customerDto,
              });
              skippedCount++;
              continue;
            } else {
              throw new AppException(
                BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
                `Số điện thoại ${customerDto.phone} đã tồn tại trong hệ thống`
              );
            }
          }

          // Kiểm tra agentId có tồn tại và thuộc về user không (nếu có)
          if (customerDto.agentId) {
            const agentExists = await this.userConvertCustomerRepository.checkAgentExists(customerDto.agentId, userId);
            if (!agentExists) {
              throw new AppException(
                BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
                `Agent với ID ${customerDto.agentId} không tồn tại hoặc không thuộc về bạn`
              );
            }
          }

          // Xử lý email - chuyển string thành object nếu cần
          const emailData = this.processEmailData(customerDto.email);

          // Validate metadata fields if provided
          const metadataToStore = await this.processMetadata(customerDto.metadata, customerDto.tags, userId);

          // Chuẩn bị dữ liệu để tạo khách hàng
          const customerData: Partial<UserConvertCustomer> = {
            name: customerDto.name,
            phone: customerDto.phone,
            email: emailData,
            platform: customerDto.platform,
            timezone: customerDto.timezone,
            userId: userId,
            agentId: customerDto.agentId,
            metadata: metadataToStore,
            facebookLink: customerDto.facebookLink,
            twitterLink: customerDto.twitterLink,
            linkedinLink: customerDto.linkedinLink,
            zaloLink: customerDto.zaloLink,
            websiteLink: customerDto.websiteLink,
            address: customerDto.address || undefined,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };

          validCustomersData.push({ data: customerData, index });

        } catch (error) {
          if (createBulkDto.continueOnError) {
            const errorCode = error instanceof AppException ? error.getErrorCode().code.toString() : 'UNKNOWN_ERROR';
            results.push({
              index,
              status: 'error',
              message: error.message,
              errorCode,
              originalData: customerDto,
            });
            errorCount++;
          } else {
            throw error;
          }
        }
      }

      // Tạo khách hàng hợp lệ
      if (validCustomersData.length > 0) {
        try {
          const createdCustomers = await this.userConvertCustomerRepository.createBulkUserConvertCustomers(
            validCustomersData.map(item => item.data)
          );

          // Tạo response cho các khách hàng đã tạo thành công
          for (let i = 0; i < createdCustomers.length; i++) {
            const customer = createdCustomers[i];
            const originalIndex = validCustomersData[i].index;
            const originalCustomerDto = createBulkDto.customers[originalIndex];
            const responseDto = await this.convertToResponseDto(customer, userId);

            results.push({
              index: originalIndex,
              status: 'success',
              customer: responseDto,
              originalData: originalCustomerDto,
            });
            successCount++;
          }
        } catch (error) {
          this.logger.error(`Lỗi khi tạo bulk khách hàng: ${error.message}`, error.stack);

          if (createBulkDto.continueOnError) {
            // Đánh dấu tất cả khách hàng hợp lệ là lỗi
            for (const item of validCustomersData) {
              results.push({
                index: item.index,
                status: 'error',
                message: `Lỗi khi tạo khách hàng: ${error.message}`,
                errorCode: 'CONVERT_CUSTOMER_BULK_CREATION_FAILED',
                originalData: createBulkDto.customers[item.index],
              });
              errorCount++;
            }
          } else {
            throw new AppException(
              BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_BULK_CREATION_FAILED,
              `Lỗi khi tạo bulk khách hàng chuyển đổi: ${error.message}`
            );
          }
        }
      }

      // Sắp xếp kết quả theo index
      results.sort((a, b) => a.index - b.index);

      const processingTimeMs = Date.now() - startTime;

      const response: BulkUserConvertCustomerResponseDto = {
        totalRequested: createBulkDto.customers.length,
        successCount,
        errorCount,
        skippedCount,
        results,
        processingTimeMs,
      };

      this.logger.log(`Hoàn thành bulk create: ${successCount} thành công, ${errorCount} lỗi, ${skippedCount} bỏ qua trong ${processingTimeMs}ms`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi khi tạo bulk khách hàng chuyển đổi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_BULK_CREATION_FAILED,
        `Lỗi khi tạo bulk khách hàng chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách khách hàng chuyển đổi của người dùng với phân trang
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách khách hàng chuyển đổi với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserConvertCustomerDto): Promise<PaginatedResult<UserConvertCustomerListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách khách hàng chuyển đổi cho userId=${userId}`);

      // Lấy danh sách khách hàng chuyển đổi từ repository
      const result = await this.userConvertCustomerRepository.findAll(userId, queryDto);

      // Get custom fields for metadata conversion
      const customFields = await this.getCustomFieldsForUser(userId);

      // Chuyển đổi sang DTO response
      const items = await Promise.all(result.items.map(async (item) => {
        const listItemDto = plainToInstance(UserConvertCustomerListItemDto, item, { excludeExtraneousValues: true });

        // Convert metadata to response format
        if (item.metadata && Object.keys(item.metadata).length > 0) {
          listItemDto.metadata = await this.convertMetadataToResponseFormat(item.metadata, customFields);
        } else {
          listItemDto.metadata = [];
        }

        return listItemDto;
      }));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách khách hàng chuyển đổi: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_FIND_FAILED,
        `Lỗi khi lấy danh sách khách hàng chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật thông tin khách hàng chuyển đổi
   * @param id ID khách hàng chuyển đổi
   * @param userId ID người dùng
   * @param updateDto DTO cập nhật khách hàng chuyển đổi
   * @returns Khách hàng chuyển đổi đã được cập nhật
   */
  async update(id: number, userId: number, updateDto: UpdateUserConvertCustomerDto): Promise<UserConvertCustomerResponseDto> {
    try {
      this.logger.log(`Cập nhật khách hàng chuyển đổi id=${id} cho userId=${userId}`);

      // Kiểm tra khách hàng chuyển đổi tồn tại và thuộc về user
      const existingCustomer = await this.userConvertCustomerRepository.findById(id);
      if (!existingCustomer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
          `Không tìm thấy khách hàng chuyển đổi với ID ${id}`
        );
      }

      if (existingCustomer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
          'Bạn không có quyền truy cập khách hàng chuyển đổi này'
        );
      }

      // Kiểm tra số điện thoại đã tồn tại chưa (nếu có cập nhật phone)
      if (updateDto.phone && updateDto.phone !== existingCustomer.phone) {
        const phoneExists = await this.userConvertCustomerRepository.findByPhone(updateDto.phone);
        if (phoneExists) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
            `Số điện thoại ${updateDto.phone} đã tồn tại trong hệ thống`
          );
        }
      }

      // Kiểm tra agentId có tồn tại và thuộc về user không (nếu có cập nhật agentId)
      if (updateDto.agentId !== undefined) {
        if (updateDto.agentId) {
          const agentExists = await this.userConvertCustomerRepository.checkAgentExists(updateDto.agentId, userId);
          if (!agentExists) {
            throw new AppException(
              BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
              `Agent với ID ${updateDto.agentId} không tồn tại hoặc không thuộc về bạn`
            );
          }
        }
      }

      // Xử lý avatar upload nếu có
      let avatarUrl: string | undefined;
      let avatarUploadInfo: { uploadUrl: string; key: string; expiresAt: number } | undefined;

      if (updateDto.avatarFile) {
        this.logger.log(`Tạo presigned URL cho avatar: ${updateDto.avatarFile.fileName}`);

        // Validate file type
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
        if (!allowedMimeTypes.includes(updateDto.avatarFile.mimeType)) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
            `Loại file không được hỗ trợ: ${updateDto.avatarFile.mimeType}`
          );
        }

        // Generate S3 key for avatar
        const s3Key = generateS3Key({
          baseFolder: userId.toString(),
          categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
          fileName: updateDto.avatarFile.fileName,
          useTimeFolder: true,
        });

        // Create presigned URL for upload
        const presignedUrl = await this.s3Service.createPresignedWithID(
          s3Key,
          TimeIntervalEnum.ONE_HOUR,
          updateDto.avatarFile.mimeType as any,
          FileSizeEnum.FIVE_MB
        );

        avatarUploadInfo = {
          uploadUrl: presignedUrl,
          key: s3Key,
          expiresAt: Date.now() + TimeIntervalEnum.ONE_HOUR,
        };

        // Generate CDN URL for the avatar
        avatarUrl = this.cdnService.generateUrlView(s3Key, TimeIntervalEnum.ONE_HOUR) || undefined;
      }

      // Handle metadata validation and conversion
      let metadataToStore: Record<string, unknown> | undefined;
      if (updateDto.metadata !== undefined) {
        if (updateDto.metadata && updateDto.metadata.length > 0) {
          // Get all custom fields for this user
          const customFields = await this.getCustomFieldsForUser(userId);

          // Validate metadata against custom field configurations
          this.validationHelper.validateMetadataFields(updateDto.metadata, customFields, userId);

          // Convert metadata to storage format
          metadataToStore = this.convertMetadataToStorageFormat(updateDto.metadata);
        } else {
          metadataToStore = {};
        }
      }

      // Handle tags - merge with existing metadata or create new
      if (updateDto.tags !== undefined) {
        if (metadataToStore === undefined) {
          metadataToStore = { ...existingCustomer.metadata };
        }
        metadataToStore.tags = updateDto.tags;
      }

      // Chuẩn bị dữ liệu để cập nhật
      const updateData: Partial<UserConvertCustomer> = {};

      if (updateDto.name !== undefined) updateData.name = updateDto.name;
      if (updateDto.phone !== undefined) updateData.phone = updateDto.phone;
      if (updateDto.email !== undefined) updateData.email = updateDto.email;
      if (avatarUrl !== undefined) updateData.avatar = avatarUrl;
      if (updateDto.platform !== undefined) updateData.platform = updateDto.platform;
      if (updateDto.timezone !== undefined) updateData.timezone = updateDto.timezone;
      if (updateDto.agentId !== undefined) updateData.agentId = updateDto.agentId;
      if (metadataToStore !== undefined) updateData.metadata = metadataToStore;

      // Cập nhật social links
      if (updateDto.facebookLink !== undefined) updateData.facebookLink = updateDto.facebookLink;
      if (updateDto.twitterLink !== undefined) updateData.twitterLink = updateDto.twitterLink;
      if (updateDto.linkedinLink !== undefined) updateData.linkedinLink = updateDto.linkedinLink;
      if (updateDto.zaloLink !== undefined) updateData.zaloLink = updateDto.zaloLink;
      if (updateDto.websiteLink !== undefined) updateData.websiteLink = updateDto.websiteLink;

      // Cập nhật địa chỉ
      if (updateDto.address !== undefined) updateData.address = updateDto.address || undefined;

      // Cập nhật khách hàng chuyển đổi
      const updatedCustomer = await this.userConvertCustomerRepository.updateUserConvertCustomer(id, updateData);

      // Chuyển đổi sang DTO response
      const responseDto = await this.convertToResponseDto(updatedCustomer, userId);

      // Thêm thông tin upload avatar nếu có
      if (avatarUploadInfo) {
        responseDto.avatarUpload = avatarUploadInfo;
      }

      this.logger.log(`Đã cập nhật khách hàng chuyển đổi thành công với ID=${id}`);
      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật khách hàng chuyển đổi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
        `Lỗi khi cập nhật khách hàng chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết khách hàng chuyển đổi theo ID
   * @param id ID khách hàng chuyển đổi
   * @param userId ID người dùng
   * @returns Chi tiết khách hàng chuyển đổi
   */
  async findById(id: number, userId: number): Promise<UserConvertCustomerResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết khách hàng chuyển đổi id=${id} cho userId=${userId}`);

      // Lấy khách hàng chuyển đổi từ repository
      const customer = await this.userConvertCustomerRepository.findById(id);

      // Kiểm tra khách hàng chuyển đổi tồn tại
      if (!customer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
          `Không tìm thấy khách hàng chuyển đổi với ID ${id}`
        );
      }

      // Kiểm tra khách hàng chuyển đổi thuộc về người dùng
      if (customer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
          `Bạn không có quyền truy cập khách hàng chuyển đổi này`
        );
      }

      // Xử lý thông tin agent nếu có
      let agentInfo: AgentInfoDto | undefined;
      if (customer.agent) {
        agentInfo = {
          id: customer.agent.id,
          name: customer.agent.name || '',
          avatar: customer.agent.avatar,
        };
      }

      // Xử lý thông tin user nếu có
      let userInfo: UserInfoDto | undefined;
      if (customer.user) {
        userInfo = {
          id: customer.user.id,
          fullName: customer.user.fullName || '',
          email: customer.user.email || '',
          phoneNumber: customer.user.phoneNumber || '',
          avatar: customer.user.avatar,
        };
      }

      // Chuyển đổi sang DTO response
      const responseDto = await this.convertToResponseDto(customer, userId);

      // Gán thông tin agent nếu có
      if (agentInfo) {
        responseDto.agent = agentInfo;
      }

      // Gán thông tin user nếu có
      if (userInfo) {
        responseDto.user = userInfo;
      }

      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết khách hàng chuyển đổi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_FIND_FAILED,
        `Lỗi khi lấy chi tiết khách hàng chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Merge hai khách hàng chuyển đổi
   * Gộp 2 customer hiện có thành 1, customer nguồn sẽ bị xóa, customer đích sẽ được cập nhật với dữ liệu merge
   * @param userId ID người dùng
   * @param mergeDto DTO merge khách hàng chuyển đổi (chứa dữ liệu cuối cùng sau merge)
   * @returns Khách hàng chuyển đổi đã được merge
   */
  async merge(userId: number, mergeDto: MergeUserConvertCustomerDto): Promise<UserConvertCustomerResponseDto> {
    try {
      this.logger.log(`Merge khách hàng chuyển đổi cho userId=${userId}, source=${mergeDto.sourceCustomerId}, target=${mergeDto.targetCustomerId}`);

      // Kiểm tra không thể merge cùng một khách hàng
      if (mergeDto.sourceCustomerId === mergeDto.targetCustomerId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_MERGE_SAME_CUSTOMER,
          'Không thể merge khách hàng với chính nó'
        );
      }

      // Lấy thông tin khách hàng nguồn
      const sourceCustomer = await this.userConvertCustomerRepository.findById(mergeDto.sourceCustomerId);
      if (!sourceCustomer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
          `Không tìm thấy khách hàng nguồn với ID ${mergeDto.sourceCustomerId}`
        );
      }

      // Lấy thông tin khách hàng đích
      const targetCustomer = await this.userConvertCustomerRepository.findById(mergeDto.targetCustomerId);
      if (!targetCustomer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
          `Không tìm thấy khách hàng đích với ID ${mergeDto.targetCustomerId}`
        );
      }

      // Kiểm tra quyền truy cập khách hàng nguồn
      if (sourceCustomer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_MERGE_DIFFERENT_USER,
          'Không có quyền merge khách hàng của người dùng khác'
        );
      }

      // Kiểm tra quyền truy cập khách hàng đích
      if (targetCustomer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_MERGE_DIFFERENT_USER,
          'Không có quyền merge khách hàng của người dùng khác'
        );
      }

      // Kiểm tra số điện thoại không trùng với khách hàng khác (ngoại trừ target customer)
      if (mergeDto.phone !== targetCustomer.phone) {
        const existingCustomer = await this.userConvertCustomerRepository.findByPhone(mergeDto.phone);
        if (existingCustomer && existingCustomer.id !== mergeDto.targetCustomerId) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
            `Số điện thoại ${mergeDto.phone} đã tồn tại trong hệ thống`
          );
        }
      }

      // Merge metadata from both customers
      let mergedMetadata: Record<string, unknown> = { ...targetCustomer.metadata };

      // Add source customer metadata
      if (sourceCustomer.metadata) {
        mergedMetadata = { ...mergedMetadata, ...sourceCustomer.metadata };
      }

      // Override with tags from merge DTO if provided
      if (mergeDto.tags) {
        mergedMetadata.tags = mergeDto.tags;
      }

      // Chuẩn bị dữ liệu để merge (giữ nguyên avatar của target customer, không merge avatar)
      const mergedData: Partial<UserConvertCustomer> = {
        name: mergeDto.name,
        phone: mergeDto.phone,
        email: mergeDto.email,
        // Không cập nhật avatar - giữ nguyên avatar của target customer
        platform: mergeDto.platform || targetCustomer.platform,
        timezone: mergeDto.timezone || targetCustomer.timezone,
        agentId: mergeDto.agentId || targetCustomer.agentId,
        metadata: mergedMetadata,
      };

      // Thực hiện merge
      const mergedCustomer = await this.userConvertCustomerRepository.mergeUserConvertCustomers(
        mergeDto.sourceCustomerId,
        mergeDto.targetCustomerId,
        mergedData
      );

      // Chuyển đổi sang DTO response
      const responseDto = await this.convertToResponseDto(mergedCustomer, userId);

      this.logger.log(`Đã merge khách hàng chuyển đổi thành công với ID=${mergedCustomer.id}`);
      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi merge khách hàng chuyển đổi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_MERGE_FAILED,
        `Lỗi khi merge khách hàng chuyển đổi: ${error.message}`
      );
    }
  }



  /**
   * Cập nhật thông tin social của khách hàng chuyển đổi
   * @param customerId ID khách hàng chuyển đổi
   * @param userId ID người dùng
   * @param updateDto DTO cập nhật thông tin social
   * @returns Thông tin social đã cập nhật
   */
  async updateSocial(customerId: number, userId: number, updateDto: UpdateCustomerSocialDto): Promise<CustomerSocialResponseDto> {
    try {
      this.logger.log(`Cập nhật thông tin social cho khách hàng ${customerId} của userId=${userId}`);

      // Kiểm tra khách hàng chuyển đổi tồn tại và thuộc về user
      const customer = await this.userConvertCustomerRepository.findById(customerId);
      if (!customer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
          `Không tìm thấy khách hàng chuyển đổi với ID ${customerId}`
        );
      }

      if (customer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
          'Bạn không có quyền truy cập khách hàng chuyển đổi này'
        );
      }

      // Xử lý Facebook data
      const facebookResults: CustomerFacebookResponseDto[] = [];
      if (updateDto.facebook && updateDto.facebook.length > 0) {
        // Xóa tất cả Facebook data hiện tại
        await this.customerFacebookRepository.deleteByUserConvertCustomerId(customerId);

        // Tạo mới Facebook data
        for (const facebookData of updateDto.facebook) {
          // Kiểm tra page scoped ID đã tồn tại chưa
          if (facebookData.pageScopedId) {
            const existingFacebook = await this.customerFacebookRepository.findByPageScopedId(facebookData.pageScopedId);
            if (existingFacebook && existingFacebook.userConvertCustomerId !== customerId) {
              throw new AppException(
                BUSINESS_ERROR_CODES.CUSTOMER_FACEBOOK_PAGE_SCOPED_ID_DUPLICATE,
                `Page scoped ID ${facebookData.pageScopedId} đã tồn tại trong hệ thống`
              );
            }
          }

          const savedFacebook = await this.customerFacebookRepository.createOrUpdate({
            ...facebookData,
            userConvertCustomerId: customerId,
          });

          facebookResults.push(plainToInstance(CustomerFacebookResponseDto, savedFacebook, { excludeExtraneousValues: true }));
        }
      } else {
        // Nếu không có Facebook data trong request, xóa tất cả
        await this.customerFacebookRepository.deleteByUserConvertCustomerId(customerId);
      }

      // Xử lý Web data
      const webResults: CustomerWebResponseDto[] = [];
      if (updateDto.web && updateDto.web.length > 0) {
        // Xóa tất cả Web data hiện tại
        await this.customerWebRepository.deleteByUserConvertCustomerId(customerId);

        // Tạo mới Web data
        for (const webData of updateDto.web) {
          const savedWeb = await this.customerWebRepository.createOrUpdate({
            ...webData,
            userConvertCustomerId: customerId,
          });

          webResults.push(plainToInstance(CustomerWebResponseDto, savedWeb, { excludeExtraneousValues: true }));
        }
      } else {
        // Nếu không có Web data trong request, xóa tất cả
        await this.customerWebRepository.deleteByUserConvertCustomerId(customerId);
      }

      const response: CustomerSocialResponseDto = {
        customerId: customerId,
        facebook: facebookResults,
        web: webResults,
      };

      this.logger.log(`Đã cập nhật thông tin social cho khách hàng ${customerId}: ${facebookResults.length} Facebook, ${webResults.length} Web`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật thông tin social: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOMER_SOCIAL_UPDATE_FAILED,
        `Lỗi khi cập nhật thông tin social: ${error.message}`
      );
    }
  }

  /**
   * Get custom fields for a specific user (for convert customers)
   * @param userId ID người dùng
   * @returns Array of custom fields for the user that are applicable to convert customers
   */
  private async getCustomFieldsForUser(userId: number): Promise<CustomField[]> {
    try {
      // Get custom fields created by this user
      const userFieldsQueryDto = {
        userId: userId,
        page: 1,
        limit: 1000, // Get all custom fields for the user
      };

      const userFieldsResult = await this.customFieldService.findAll(userFieldsQueryDto);

      // Get all custom fields without any filter to get employee-created fields
      // We'll filter them afterwards to avoid complex query logic
      const allFieldsQueryDto = {
        page: 1,
        limit: 1000,
      };

      const allFieldsResult = await this.customFieldService.findAll(allFieldsQueryDto);

      // Combine user-created and employee-created fields
      const userFields = userFieldsResult.items || [];

      // Get employee-created fields (fields where employeeId is not null and userId is null or different from current user)
      const employeeFields = (allFieldsResult.items || []).filter(item =>
        item.employeeId !== null && item.employeeId !== undefined &&
        (item.userId === null || item.userId !== userId)
      );

      // Combine all fields and remove duplicates by configId (prioritize user fields over employee fields)
      const allFields = [...userFields, ...employeeFields];
      const uniqueFields = allFields.reduce((acc, current) => {
        const existing = acc.find(item => item.configId === current.configId);
        if (!existing) {
          acc.push(current);
        }
        return acc;
      }, [] as typeof allFields);

      // Filter chỉ lấy các custom fields có status PENDING hoặc APPROVED (loại bỏ DELETED, REJECTED)
      const activeFields = uniqueFields.filter(item =>
        item.status === 'PENDING' || item.status === 'APPROVED'
      );

      this.logger.log(`Tìm thấy ${activeFields.length}/${uniqueFields.length} custom fields active cho userId ${userId} (bao gồm ${userFields.length} user fields và ${employeeFields.length} employee fields)`);

      // Debug: Log all configIds to see what's available
      const configIds = activeFields.map(field => field.configId);
      this.logger.log(`Available configIds: ${JSON.stringify(configIds)}`);

      return activeFields.map(item => ({
        id: item.id,
        configId: item.configId,
        label: item.label,
        type: item.type,
        required: item.required,
        configJson: item.configJson,
        userId: item.userId,
        employeeId: item.employeeId,
        status: item.status,
        createAt: item.createAt,
        tags: [], // CustomFieldListItemDto doesn't have tags property, set empty array
        component: item.component,
      } as CustomField));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy custom fields cho userId ${userId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Convert metadata fields from DTO format to storage format
   * @param metadata Array of metadata fields
   * @returns Object with configId as key and value as value
   */
  private convertMetadataToStorageFormat(metadata: MetadataFieldDto[]): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const field of metadata) {
      result[field.configId] = field.value;
    }

    return result;
  }

  /**
   * Convert metadata from storage format to response format
   * @param metadata Stored metadata object
   * @param customFields Array of custom field configurations
   * @returns Array of metadata field response DTOs
   */
  private async convertMetadataToResponseFormat(
    metadata: Record<string, unknown>,
    customFields: CustomField[]
  ): Promise<MetadataFieldResponseDto[]> {
    const result: MetadataFieldResponseDto[] = [];

    // Create a map of custom fields by configId for quick lookup
    const customFieldMap = new Map<string, CustomField>();
    customFields.forEach(field => customFieldMap.set(field.configId, field));

    // Convert each metadata field
    for (const [configId, value] of Object.entries(metadata)) {
      // Skip tags and other non-custom-field metadata
      if (configId === 'tags') {
        continue;
      }

      const customField = customFieldMap.get(configId);
      if (customField) {
        result.push({
          configId,
          value: value as string | number | boolean | string[] | number[],
          label: customField.label,
          type: customField.type,
          required: customField.required,
        });
      }
    }

    return result;
  }

  /**
   * Helper method to process email data - convert string to object if needed
   * @param email Email data from DTO
   * @returns Processed email data
   */
  private processEmailData(email: string | Record<string, string> | undefined): Record<string, string> | undefined {
    if (!email) return undefined;

    if (typeof email === 'string') {
      return { primary: email };
    }
    return email;
  }

  /**
   * Helper method to process and validate metadata
   * @param metadata Metadata from DTO
   * @param tags Tags from DTO
   * @param userId User ID for validation
   * @returns Processed metadata for storage
   */
  private async processMetadata(
    metadata: MetadataFieldDto[] | undefined,
    tags: string[] | undefined,
    userId: number
  ): Promise<Record<string, unknown>> {
    let metadataToStore: Record<string, unknown> = {};

    if (metadata && metadata.length > 0) {
      // Get all custom fields for this user
      const customFields = await this.getCustomFieldsForUser(userId);

      // Validate metadata against custom field configurations
      this.validationHelper.validateMetadataFields(metadata, customFields, userId);

      // Convert metadata to storage format
      metadataToStore = this.convertMetadataToStorageFormat(metadata);
    }

    // Add tags to metadata if provided
    if (tags && tags.length > 0) {
      metadataToStore.tags = tags;
    }

    return metadataToStore;
  }

  /**
   * Helper method to convert customer entity to response DTO with metadata
   * @param customer Customer entity
   * @param userId User ID for custom fields lookup
   * @returns Response DTO with converted metadata
   */
  private async convertToResponseDto(
    customer: UserConvertCustomer,
    userId: number
  ): Promise<UserConvertCustomerResponseDto> {
    const responseDto = plainToInstance(UserConvertCustomerResponseDto, customer, { excludeExtraneousValues: true });

    // Convert metadata to response format
    if (customer.metadata && Object.keys(customer.metadata).length > 0) {
      const customFields = await this.getCustomFieldsForUser(userId);
      responseDto.metadata = await this.convertMetadataToResponseFormat(customer.metadata, customFields);
    } else {
      responseDto.metadata = [];
    }

    return responseDto;
  }

  /**
   * Xóa khách hàng chuyển đổi (soft delete)
   * @param customerId ID khách hàng chuyển đổi cần xóa
   * @param userId ID người dùng hiện tại
   */
  async deleteCustomer(customerId: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa khách hàng chuyển đổi ${customerId} bởi user ${userId}`);

      // Kiểm tra khách hàng tồn tại và thuộc về user hiện tại
      const customer = await this.userConvertCustomerRepository.findByIdAndUserId(customerId, userId);
      if (!customer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.USER_CONVERT_CUSTOMER_NOT_FOUND,
          `Không tìm thấy khách hàng chuyển đổi với ID ${customerId}`
        );
      }

      // Soft delete - cập nhật trạng thái thành DELETED
      customer.status = 'DELETED';
      customer.updatedAt = Date.now();
      await this.userConvertCustomerRepository.save(customer);

      this.logger.log(`Đã xóa khách hàng chuyển đổi ${customerId}`);

    } catch (error) {
      this.logger.error(`Lỗi khi xóa khách hàng chuyển đổi: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.USER_CONVERT_CUSTOMER_DELETE_FAILED,
        `Lỗi khi xóa khách hàng chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Xóa nhiều khách hàng chuyển đổi (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID khách hàng chuyển đổi cần xóa
   * @param userId ID người dùng hiện tại
   * @returns Kết quả xóa nhiều khách hàng chuyển đổi
   */
  async bulkDeleteCustomers(
    bulkDeleteDto: BulkDeleteUserConvertCustomerDto,
    userId: number,
  ): Promise<BulkDeleteUserConvertCustomerResponseDto> {
    try {
      const { customerIds } = bulkDeleteDto;
      const results: any[] = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(
        `Bắt đầu xóa bulk ${customerIds.length} khách hàng chuyển đổi cho userId=${userId}`,
      );

      // Xử lý từng khách hàng một để có thể báo cáo chi tiết
      for (const customerId of customerIds) {
        try {
          // Kiểm tra khách hàng tồn tại và thuộc về user hiện tại
          const customer = await this.userConvertCustomerRepository.findByIdAndUserId(customerId, userId);

          if (!customer) {
            results.push({
              customerId,
              status: 'error',
              message: `Không tìm thấy khách hàng chuyển đổi với ID ${customerId}`,
            });
            failureCount++;
            continue;
          }

          // Soft delete - cập nhật trạng thái thành DELETED
          customer.status = 'DELETED';
          customer.updatedAt = Date.now();
          await this.userConvertCustomerRepository.save(customer);

          results.push({
            customerId,
            status: 'success',
            message: 'Xóa khách hàng chuyển đổi thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(
            `Lỗi khi xóa khách hàng chuyển đổi ${customerId}: ${error.message}`,
            error.stack,
          );

          results.push({
            customerId,
            status: 'error',
            message: error instanceof AppException ? error.message : `Lỗi khi xóa khách hàng chuyển đổi: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeleteUserConvertCustomerResponseDto = {
        totalRequested: customerIds.length,
        successCount,
        failureCount,
        results,
        message: `Xóa thành công ${successCount}/${customerIds.length} khách hàng chuyển đổi`,
      };

      this.logger.log(
        `Hoàn thành xóa bulk khách hàng chuyển đổi: ${successCount} thành công, ${failureCount} thất bại`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.USER_CONVERT_CUSTOMER_DELETE_FAILED,
        `Lỗi khi xóa bulk khách hàng chuyển đổi: ${error.message}`,
      );
    }
  }
}
