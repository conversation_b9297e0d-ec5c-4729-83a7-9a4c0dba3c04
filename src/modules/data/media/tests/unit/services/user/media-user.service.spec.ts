import { Test, TestingModule } from '@nestjs/testing';
import { MediaUserService } from '../../../../user/services/media-user.service';
import { MediaRepository } from '../../../../repositories';
import { S3Service } from '@/shared/services/s3.service';
import { AgentMediaRepository } from '@modules/agent/repositories';
import { Media } from '../../../../entities/media.entity';
import { QueryDto, SortDirection } from '@common/dto';
import { MediaDto } from '../../../../dto/media.dto';
import { AppException } from '@/common';
import { MEDIA_ERROR_CODES } from '@modules/data/media/exception';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { CategoryFolderEnum, FileSizeEnum, MediaType, TimeIntervalEnum } from '@/shared/utils';

describe('MediaUserService', () => {
  let service: MediaUserService;
  let mediaRepository: jest.Mocked<MediaRepository>;
  let s3Service: jest.Mocked<S3Service>;
  let agentMediaRepository: jest.Mocked<AgentMediaRepository>;

  // Mock data
  const mockMedia: Media = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Test Media',
    description: 'Test Description',
    size: 1024,
    tags: ['test', 'media'],
    storageKey: 'media/test/123e4567-e89b-12d3-a456-426614174000.jpg',
    ownedBy: 1,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
    nameEmbedding: [],
    descriptionEmbedding: [],
    status: 'DRAFT' as any,
  };

  const mockMediaList: Media[] = [mockMedia];

  const mockPaginatedResult: PaginatedResult<Media> = {
    items: mockMediaList,
    meta: {
      totalItems: mockMediaList.length,
      itemCount: mockMediaList.length,
      itemsPerPage: mockMediaList.length,
      totalPages: 1,
      currentPage: 1
    }
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaUserService,
        {
          provide: MediaRepository,
          useValue: {
            findOneBy: jest.fn(),
            findOne: jest.fn(),
            findAllUserMedia: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            createPresignedWithID: jest.fn(),
            deleteFiles: jest.fn(),
          },
        },
        {
          provide: AgentMediaRepository,
          useValue: {
            delete: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<MediaUserService>(MediaUserService);
    mediaRepository = module.get(MediaRepository) as jest.Mocked<MediaRepository>;
    s3Service = module.get(S3Service) as jest.Mocked<S3Service>;
    agentMediaRepository = module.get(AgentMediaRepository) as jest.Mocked<AgentMediaRepository>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findById', () => {
    it('should return media by id when user is the owner', async () => {
      // Arrange
      const mediaId = '123e4567-e89b-12d3-a456-426614174000';
      const userId = 1;
      mediaRepository.findOneBy.mockResolvedValue(mockMedia);

      // Act
      const result = await service.findById(mediaId, userId);

      // Assert
      expect(mediaRepository.findOneBy).toHaveBeenCalledWith({ id: mediaId });
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockMedia);
    });

    it('should throw NOT_FOUND exception when media does not exist', async () => {
      // Arrange
      const mediaId = 'non-existent-id';
      const userId = 1;
      mediaRepository.findOneBy.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById(mediaId, userId)).rejects.toThrow(AppException);
      expect(mediaRepository.findOneBy).toHaveBeenCalledWith({ id: mediaId });
    });

    it('should throw FORBIDDEN exception when user is not the owner', async () => {
      // Arrange
      const mediaId = '123e4567-e89b-12d3-a456-426614174000';
      const userId = 2; // Different from media.ownedBy
      mediaRepository.findOneBy.mockResolvedValue(mockMedia);

      // Act & Assert
      await expect(service.findById(mediaId, userId)).rejects.toThrow(AppException);
      expect(mediaRepository.findOneBy).toHaveBeenCalledWith({ id: mediaId });
    });
  });

  describe('findAllByUser', () => {
    it('should return paginated media list for user', async () => {
      // Arrange
      const userId = 1;
      const query: QueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      };
      mediaRepository.findAllUserMedia.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await service.findAllByUser(userId, query);

      // Assert
      expect(mediaRepository.findAllUserMedia).toHaveBeenCalledWith(userId, expect.objectContaining({
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      }));
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should use default values when query parameters are not provided', async () => {
      // Arrange
      const userId = 1;
      const query: QueryDto = {}; // Empty query
      mediaRepository.findAllUserMedia.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await service.findAllByUser(userId, query);

      // Assert
      expect(mediaRepository.findAllUserMedia).toHaveBeenCalledWith(userId, expect.objectContaining({
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      }));
      expect(result).toBeInstanceOf(ApiResponseDto);
    });
  });

  describe('deleteManyByUser', () => {
    it('should delete multiple media files owned by user', async () => {
      // Arrange
      const userId = 1;
      const keys = ['media/key1.jpg', 'media/key2.jpg'];
      const s3DeleteResult = {
        deleted: keys,
        errors: [],
      };
      s3Service.deleteFiles.mockResolvedValue(s3DeleteResult);
      mediaRepository.findOne.mockResolvedValueOnce(mockMedia).mockResolvedValueOnce({...mockMedia, id: '456'});

      // Act
      const result = await service.deleteManyByUser(userId, keys);

      // Assert
      expect(s3Service.deleteFiles).toHaveBeenCalledWith(keys);
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(2);
      expect(agentMediaRepository.delete).toHaveBeenCalledTimes(2);
      expect(mediaRepository.delete).toHaveBeenCalledTimes(2);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result.deleted).toEqual(keys);
      expect(result.result.errors).toEqual([]);
    });

    it('should throw BAD_REQUEST exception when keys array is empty', async () => {
      // Arrange
      const userId = 1;
      const keys = [];

      // Act & Assert
      await expect(service.deleteManyByUser(userId, keys)).rejects.toThrow(AppException);
    });

    it('should handle exceptions from S3 deletion', async () => {
      // Arrange
      const userId = 1;
      const keys = ['media/key1.jpg', 'media/key2.jpg'];
      const s3DeleteResult = {
        deleted: ['media/key1.jpg'],
        errors: [{ key: 'media/key2.jpg', message: 'File not found' }],
      };
      s3Service.deleteFiles.mockResolvedValue(s3DeleteResult);
      mediaRepository.findOne.mockResolvedValueOnce(mockMedia);

      // Act
      const result = await service.deleteManyByUser(userId, keys);

      // Assert
      expect(s3Service.deleteFiles).toHaveBeenCalledWith(keys);
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result.deleted).toEqual(['media/key1.jpg']);
      expect(result.result.errors).toEqual([{ key: 'media/key2.jpg', message: 'File not found' }]);
    });

    it('should skip deletion in DB if media is not found', async () => {
      // Arrange
      const userId = 1;
      const keys = ['media/key1.jpg'];
      const s3DeleteResult = {
        deleted: keys,
        errors: [],
      };
      s3Service.deleteFiles.mockResolvedValue(s3DeleteResult);
      mediaRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await service.deleteManyByUser(userId, keys);

      // Assert
      expect(s3Service.deleteFiles).toHaveBeenCalledWith(keys);
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(1);
      expect(agentMediaRepository.delete).not.toHaveBeenCalled();
      expect(mediaRepository.delete).not.toHaveBeenCalled();
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result.deleted).toEqual(keys);
    });

    it('should skip deletion in DB if user is not the owner', async () => {
      // Arrange
      const userId = 2; // Different from media.ownedBy
      const keys = ['media/key1.jpg'];
      const s3DeleteResult = {
        deleted: keys,
        errors: [],
      };
      s3Service.deleteFiles.mockResolvedValue(s3DeleteResult);
      mediaRepository.findOne.mockResolvedValue(mockMedia);

      // Act
      const result = await service.deleteManyByUser(userId, keys);

      // Assert
      expect(s3Service.deleteFiles).toHaveBeenCalledWith(keys);
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(1);
      expect(agentMediaRepository.delete).not.toHaveBeenCalled();
      expect(mediaRepository.delete).not.toHaveBeenCalled();
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result.deleted).toEqual(keys);
    });
  });

  describe('createPresignedUrlsFromMediaList', () => {
    it('should create presigned URLs for media list', async () => {
      // Arrange
      const userId = 1;
      const mediaList: MediaDto[] = [
        {
          name: 'test-image.jpg',
          description: 'Test image',
          size: 1024,
          tags: ['test', 'image'],
          type: 'image/jpeg',
          storageKey: '',
          ownedBy: userId,
        },
      ];
      const presignedUrl = 'https://presigned-url.example.com';
      s3Service.createPresignedWithID.mockResolvedValue(presignedUrl);
      mediaRepository.save.mockResolvedValue([]);

      // Act
      const result = await service.createPresignedUrlsFromMediaList(mediaList, userId);

      // Assert
      expect(s3Service.createPresignedWithID).toHaveBeenCalledTimes(1);
      expect(mediaRepository.save).toHaveBeenCalledTimes(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual([presignedUrl]);
    });

    it('should throw FILE_SIZE_EXCEEDED exception when file size exceeds limit', async () => {
      // Arrange
      const userId = 1;
      const mediaList: MediaDto[] = [
        {
          name: 'large-image.jpg',
          description: 'Large image',
          size: FileSizeEnum.ONE_MB + 1, // Exceeds limit for image
          tags: ['test', 'image'],
          type: 'image/jpeg',
          storageKey: '',
          ownedBy: userId,
        },
      ];

      // Act & Assert
      await expect(service.createPresignedUrlsFromMediaList(mediaList, userId)).rejects.toThrow(AppException);
      expect(s3Service.createPresignedWithID).not.toHaveBeenCalled();
      expect(mediaRepository.save).not.toHaveBeenCalled();
    });

    it('should throw FILE_TYPE_NOT_FOUND exception for unsupported media type', async () => {
      // Arrange
      const userId = 1;
      const mediaList: MediaDto[] = [
        {
          name: 'unknown-file.xyz',
          description: 'Unknown file type',
          size: 1024,
          tags: ['test'],
          type: 'application/unknown',
          storageKey: '',
          ownedBy: userId,
        },
      ];

      // Act & Assert
      await expect(service.createPresignedUrlsFromMediaList(mediaList, userId)).rejects.toThrow(AppException);
      expect(s3Service.createPresignedWithID).not.toHaveBeenCalled();
      expect(mediaRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('resolveMediaConfig', () => {
    it('should return correct config for image type', () => {
      // Arrange & Act
      const result = (service as any).resolveMediaConfig('image/jpeg' as MediaType);

      // Assert
      expect(result).toEqual({
        mimeType: 'image/jpeg',
        expirationTime: TimeIntervalEnum.ONE_DAY,
        maxSize: FileSizeEnum.ONE_MB,
        CategoryFolderEnum: CategoryFolderEnum.IMAGE,
      });
    });

    it('should return correct config for video type', () => {
      // Arrange & Act
      const result = (service as any).resolveMediaConfig('video/mp4' as MediaType);

      // Assert
      expect(result).toEqual({
        mimeType: 'video/mp4',
        expirationTime: TimeIntervalEnum.TWO_DAYS,
        maxSize: FileSizeEnum.TWENTY_MB,
        CategoryFolderEnum: CategoryFolderEnum.VIDEO,
      });
    });

    it('should return correct config for audio type', () => {
      // Arrange & Act
      const result = (service as any).resolveMediaConfig('audio/mpeg' as MediaType);

      // Assert
      expect(result).toEqual({
        mimeType: 'audio/mpeg',
        expirationTime: TimeIntervalEnum.FIFTEEN_MINUTES,
        maxSize: FileSizeEnum.TWENTY_MB,
        CategoryFolderEnum: CategoryFolderEnum.AUDIO,
      });
    });

    it('should throw FILE_TYPE_NOT_FOUND for unsupported type', () => {
      // Arrange & Act & Assert
      expect(() => (service as any).resolveMediaConfig('unknown/type' as MediaType)).toThrow(AppException);
    });

    it('should throw FILE_TYPE_NOT_FOUND for document types (no longer supported)', () => {
      // Arrange & Act & Assert
      expect(() => (service as any).resolveMediaConfig('application/pdf' as MediaType)).toThrow(AppException);
      expect(() => (service as any).resolveMediaConfig('application/msword' as MediaType)).toThrow(AppException);
      expect(() => (service as any).resolveMediaConfig('text/plain' as MediaType)).toThrow(AppException);
    });
  });
});
