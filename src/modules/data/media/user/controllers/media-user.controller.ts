import { Body, Controller, Delete, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { MediaUserService } from '../services/media-user.service';
import { MediaDto } from '../../dto/media-user.dto';
import { MediaQueryDto } from '@/modules/data/media/dto/media-query.dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { MEDIA_ERROR_CODES } from '../../exception';
import { ErrorCode } from '@/common';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { DeleteMediaDto } from '../../dto/delete-media.dto';
import { ConfirmUploadDto, ConfirmUploadResponseDto } from '../../dto/confirm-upload.dto';

/**
 * Controller handling APIs related to user media management
 */
@ApiTags(SWAGGER_API_TAGS.USER_MEDIA)
@ApiExtraModels(ApiResponseDto, MediaDto, PaginatedResult, ApiErrorResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('media')
export class MediaUserController {
  constructor(private readonly mediaUserService: MediaUserService) {}

  /**
   * Get list of media for the current user
   */
  @Get('my-media')
  @ApiOperation({ summary: 'Get list of media for the current user' })
  // @ApiQuery({ type: MediaQueryDto })
  @ApiResponse({
    status: 200,
    description: 'List of media retrieved successfully.',
    schema: ApiResponseDto.getPaginatedSchema(MediaDto),
  })
  @ApiErrorResponse(MEDIA_ERROR_CODES.DATA_FETCH_ERROR)
  async findMyMedia(
    @CurrentUser() user: JwtPayload,
    @Query() query: MediaQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<MediaDto>>> {
    const result = await this.mediaUserService.findAllByUser(user.sub, query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Get media details by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get media details by ID' })
  @ApiParam({ name: 'id', type: 'string', description: 'ID of the media' })
  @ApiResponse({
    status: 200,
    description: 'Media details retrieved successfully.',
    schema: ApiResponseDto.getSchema(MediaDto),
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.NOT_FOUND,
    MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
  )
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<MediaDto>> {
    const result = await this.mediaUserService.findById(id, user.sub);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa mềm nhiều media cho người dùng hiện tại (cập nhật trạng thái thành DELETED)
   */
  @Delete('my-media')
  @ApiOperation({ summary: 'Xóa mềm nhiều media cho người dùng hiện tại' })
  @ApiBody({ type: DeleteMediaDto })
  @ApiResponse({
    status: 200,
    description: 'Media đã được xóa mềm thành công.',
    schema: ApiResponseDto.getSchema(null),
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.BAD_REQUEST,
    MEDIA_ERROR_CODES.BAD_REQUEST,
  )
  async deleteManyMyMedia(
    @CurrentUser() user: JwtPayload,
    @Body() dto: DeleteMediaDto,
  ): Promise<ApiResponseDto<null>> {
    await this.mediaUserService.deleteManyByUser(user.id, dto.mediaIds);
    return ApiResponseDto.success(null, 'Media đã được xóa mềm thành công.');
  }

  /**
   * Create presigned URLs for media
   */
  @Post('presigned-urls')
  @ApiOperation({ summary: 'Create presigned URLs for media' })
  @ApiBody({
    type: MediaDto,
    isArray: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Presigned URLs created successfully.',
    schema: ApiResponseDto.getSchema([String]),
  })
  @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  async createPresignedUrlsFromMediaList(
    @CurrentUser() user: JwtPayload,
    @Body() mediaList: MediaDto[],
  ): Promise<ApiResponseDto<string[]>> {
    const result = await this.mediaUserService.createPresignedUrlsFromMediaList(
      mediaList,
      user.sub,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Confirm upload success and update media status to APPROVED
   */
  @Post('confirm-upload')
  @ApiOperation({
    summary: 'Confirm upload success and update media status to APPROVED',
    description: 'Call this endpoint after successfully uploading files to S3 using presigned URLs'
  })
  @ApiBody({ type: ConfirmUploadDto })
  @ApiResponse({
    status: 200,
    description: 'Upload confirmed successfully.',
    schema: ApiResponseDto.getSchema(ConfirmUploadResponseDto),
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.BAD_REQUEST,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async confirmUploadSuccess(
    @CurrentUser() user: JwtPayload,
    @Body() dto: ConfirmUploadDto,
  ): Promise<ApiResponseDto<ConfirmUploadResponseDto>> {
    const result = await this.mediaUserService.confirmUploadSuccess(
      user.sub,
      dto.storageKeys,
    );

    const response: ConfirmUploadResponseDto = {
      updatedIds: result.updatedIds,
      skippedKeys: result.skippedKeys,
      totalUpdated: result.updatedIds.length,
      totalSkipped: result.skippedKeys.length,
    };

    return ApiResponseDto.success(response, 'Upload confirmed successfully.');
  }
}
