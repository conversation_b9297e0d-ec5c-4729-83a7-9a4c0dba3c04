import { CdnService } from '@/shared/services/cdn.service';
import { Injectable, Logger } from '@nestjs/common';
import { DeepPartial } from 'typeorm';
import { Media } from '../../entities/media.entity';
import { MediaRepository } from '../../repositories';
import { MediaDto } from '../../dto/media-user.dto';
import { S3Service } from '@/shared/services/s3.service';
import {
  CategoryFolderEnum,
  FileSizeEnum,
  generateS3Key, ImageType,
  MediaType,
  TimeIntervalEnum, VideoType, AudioType,
} from '@/shared/utils';
import { SortDirection } from '@common/dto';
import { MediaQueryDto } from '@/modules/data/media/dto/media-query.dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { MediaUploadUrlDto, PresignedUrlResponseDto } from '../../dto/media-upload-url.dto';
import { MEDIA_ERROR_CODES } from '@modules/data/media/exception';
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { MediaValidationHelper } from '../../helpers/validation.helper';
import { MediaUserMapper } from '../../mappers/media-user.mapper';
import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';
import { Transactional } from 'typeorm-transactional';

/**
 * Interface định nghĩa cấu trúc trả về của phương thức resolveMediaConfig
 */
interface MediaConfig {
  mimeType: MediaType;
  expirationTime: TimeIntervalEnum;
  maxSize: FileSizeEnum;
  CategoryFolderEnum: CategoryFolderEnum;
}

@Injectable()
export class MediaUserService {
  private logger = new Logger(MediaUserService.name);

  constructor(
    private readonly mediaRepository: MediaRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly mediaValidationHelper :MediaValidationHelper,
  ) {}

  async findById(id: string, userId: number): Promise<MediaDto> {
    const media = await this.mediaRepository.findOneBy({ id });

    if (!media) {
      throw new AppException(
        MEDIA_ERROR_CODES.NOT_FOUND,
        `Media với id ${id} không tồn tại.`,
      );
    }

    if (media.ownedBy !== userId) {
      throw new AppException(
        MEDIA_ERROR_CODES.FORBIDDEN,
        'Bạn không có quyền xem media này.',
      );
    }

    return await MediaUserMapper.toUserDto(media, this.cdnService);
  }

  /**
   * Xóa mềm nhiều media theo danh sách ID (cập nhật trạng thái thành DELETED)
   * @param userId ID của người dùng đang thực hiện xóa
   * @param mediaIds Danh sách ID của media cần xóa
   * @returns Kết quả xóa mềm
   */
  @Transactional()
  async deleteManyByUser(
    userId: number,
    mediaIds: string[],
  ): Promise<ApiResponseDto<null>> {
    // Kiểm tra danh sách ID không được rỗng
    this.mediaValidationHelper.validateMediaLength(
      mediaIds.length,
      'Danh sách ID media không được rỗng.'
    );

    const deletedIds: string[] = [];
    const skippedIds: string[] = [];
    const failedIds: { id: string; reason: string }[] = [];

    // Xử lý từng media ID
    for (const mediaId of mediaIds) {
      try {
        // Tìm kiếm media theo ID
        const media = await this.mediaRepository.findOne({
          where: { id: mediaId },
        });

        if (!media) {
          skippedIds.push(mediaId);
          continue; // Nếu media không tồn tại trong DB, bỏ qua
        }

        // Kiểm tra quyền sở hữu
        if (!this.mediaValidationHelper.validateUserPermission(userId, media.ownedBy)) {
          skippedIds.push(mediaId);
          continue; // Nếu không phải chủ sở hữu, bỏ qua
        }

        // Cập nhật trạng thái thành DELETED thay vì xóa hoàn toàn
        await this.mediaRepository.update(
          { id: mediaId },
          {
            status: MediaStatusEnum.DELETED,
            updatedAt: Date.now()
          }
        );

        deletedIds.push(mediaId);
      } catch (error) {
        this.logger.error(`Lỗi khi xóa mềm media ${mediaId}: ${error.message}`, error.stack);
        failedIds.push({ id: mediaId, reason: error.message });
      }
    }

    this.logger.log(`Xóa mềm media: đã xóa ${deletedIds.length}, bỏ qua ${skippedIds.length}, lỗi ${failedIds.length}`);

    return ApiResponseDto.success(null, 'Media đã được xóa mềm thành công.');
  }

  /**
   * Lấy danh sách media thuộc về người dùng cụ thể với phân trang, tìm kiếm và sắp xếp.
   *
   * @param userId - ID của người dùng đang đăng nhập
   * @param query - Tham số query gồm phân trang, tìm kiếm, sắp xếp
   * @returns ApiResponseDto chứa danh sách media phân trang
   */
  async findAllByUser(
    userId: number,
    query: MediaQueryDto,
  ): Promise<PaginatedResult<MediaDto>>{
    // Thiết lập các tham số mặc định và ghi đè nếu có truyền vào từ query
    const options: MediaQueryDto = {
      page: query.page || 1, // Trang hiện tại (mặc định 1)
      limit: query.limit || 10, // Số bản ghi mỗi trang (mặc định 10)
      search: query.search, // Từ khóa tìm kiếm nếu có
      sortBy: query.sortBy || 'createdAt', // Trường sắp xếp (mặc định 'createdAt')
      sortDirection: query.sortDirection || SortDirection.DESC, // Hướng sắp xếp (mặc định DESC)
    };

    // Gọi repository để lấy danh sách media của người dùng theo điều kiện đã thiết lập
    const result = await this.mediaRepository.findAllUserMedia(userId, options);

    // Trả về kết quả dạng ApiResponseDto có phân trang
    return {
      items: await MediaUserMapper.toUserList(result.items, this.cdnService),
      meta: result.meta,
    }
  }

  async createPresignedUrlsFromMediaList(
    mediaList: MediaDto[],
    userId: number,
  ): Promise<string[]>{
    const presignedUrls: string[] = [];
    const mediaEntities: DeepPartial<Media>[] = [];

    for (const media of mediaList) {
      // Validate MIME type - name là tên mô tả, không phải file name
      this.mediaValidationHelper.validateAllowedMimeType(media.type);

      const { mimeType, expirationTime, maxSize, CategoryFolderEnum } =
        this.resolveMediaConfig(media.type);

      // Tạo file name từ name và type
      const extension = this.getFileExtensionFromMimeType(media.type);
      const fileName = `${media.name.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`;

      const storageKey = generateS3Key({
        baseFolder: 'media',
        categoryFolder: CategoryFolderEnum,
        fileName: fileName,
        prefix: `user_${userId}`,
        useTimeFolder: true,
      });

      // Kiểm tra kích thước file
      this.mediaValidationHelper.validateMediaSize(
        media.size,
        maxSize,
      );


      const presignedUrl = await this.s3Service.createPresignedWithID(
        storageKey,
        expirationTime,
        mimeType,
        maxSize,
      );

      presignedUrls.push(presignedUrl);

      const mediaEntity: DeepPartial<Media> = {
        name: media.name,
        description: media.description,
        size: media.size,
        tags: media.tags,
        ownedBy: userId,
        status: MediaStatusEnum.PENDING,
        storageKey,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      mediaEntities.push(mediaEntity);
    }

    await this.mediaRepository.save(mediaEntities);

    return (presignedUrls);
  }

  /**
   * Tạo URL tạm thời để upload tài nguyên media
   * @param userId ID của người dùng đang đăng nhập
   * @param dto Thông tin về loại media và kích thước file
   * @returns Thông tin URL tạm thời và key
   */
  async createMediaUploadUrl(
    userId: number,
    dto: MediaUploadUrlDto,
  ): Promise<PresignedUrlResponseDto> {
    try {
      // Xác định loại media và thư mục phù hợp
      let mediaType: MediaType;
      let categoryFolder: CategoryFolderEnum;

      // Validate MIME type và file name nếu có
      this.mediaValidationHelper.validateAllowedMimeType(dto.mediaType);
      if (dto.fileName) {
        this.mediaValidationHelper.validateAllowedFileExtension(dto.fileName);
      }

      // Xác định loại media từ MIME type - chỉ hỗ trợ image, video và audio
      if (dto.mediaType.startsWith('image/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.IMAGE;
      } else if (dto.mediaType.startsWith('video/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.VIDEO;
      } else if (dto.mediaType.startsWith('audio/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.AUDIO;
      } else {
        // Không bao giờ đến đây vì đã kiểm tra ở trên
        throw new AppException(
          MEDIA_ERROR_CODES.FILE_TYPE_NOT_FOUND,
          `Loại media không được hỗ trợ: ${dto.mediaType}`,
        );
      }

      // Tạo tên file nếu không được cung cấp
      const fileName = dto.fileName || `file-${Date.now()}`;

      // Tạo S3 key cho file
      const key = generateS3Key({
        baseFolder: 'media',
        categoryFolder: categoryFolder,
        fileName: fileName,
        prefix: `user_${userId}`,
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const uploadUrl = await this.s3Service.createPresignedWithID(
        key,
        expirationTime,
        mediaType,
        dto.fileSize,
      );

      // Tính thời gian hết hạn
      const expiresAt = Date.now() + expirationTime * 1000;

      return {
        uploadUrl,
        key,
        expiresAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo URL upload: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
        `Không thể tạo URL upload: ${error.message}`,
      );
    }
  }

  /**
   * Xác định cấu hình media dựa trên loại media
   * Chỉ hỗ trợ Image, Video và Audio - không hỗ trợ Document files
   * @param type Loại media
   * @returns Cấu hình media bao gồm mimeType, expirationTime, maxSize, và CategoryFolderEnum
   * @throws AppException nếu loại media không được hỗ trợ
   */
  private resolveMediaConfig(type: string): MediaConfig {
    // Thử xử lý như hình ảnh
    try {
      const typeImage: MediaType = ImageType.getType(type);
      return {
        mimeType: typeImage,
        expirationTime: TimeIntervalEnum.FIVE_MINUTES,
        maxSize: FileSizeEnum.FIVE_MB,
        CategoryFolderEnum: CategoryFolderEnum.IMAGE,
      };
    } catch (error) {
      // Thử xử lý như video
      try {
        const typeVideo: MediaType = VideoType.getType(type);
        return {
          mimeType: typeVideo,
          expirationTime: TimeIntervalEnum.FIFTEEN_MINUTES,
          maxSize: FileSizeEnum.FIFTY_MB,
          CategoryFolderEnum: CategoryFolderEnum.VIDEO,
        };
      } catch (error) {
        // Thử xử lý như audio
        try {
          const typeAudio: MediaType = AudioType.getType(type);
          return {
            mimeType: typeAudio,
            expirationTime: TimeIntervalEnum.FIFTEEN_MINUTES,
            maxSize: FileSizeEnum.TWENTY_MB,
            CategoryFolderEnum: CategoryFolderEnum.AUDIO,
          };
        } catch (innerError) {
          // Nếu tất cả các trường hợp đều thất bại, ném lỗi
          throw new AppException(
            MEDIA_ERROR_CODES.FILE_TYPE_NOT_FOUND,
            `Loại media không được hỗ trợ. Chỉ hỗ trợ hình ảnh, video và audio: ${type}`
          );
        }
      }
    }
  }
}
