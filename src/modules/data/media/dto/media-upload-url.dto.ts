import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { VideoTypeEnum } from '@shared/utils/file/video-media-type.util';
import { AudioTypeEnum } from '@shared/utils/file/audio-media-type.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo URL tạm thời để upload tài nguyên
 */
export class MediaUploadUrlDto {
  @ApiProperty({
    description: 'Loại media (MIME type) - Chỉ hỗ trợ hình ảnh, video và audio',
    example: 'image/jpeg',
    oneOf: [
      { enum: Object.values(ImageTypeEnum) },
      { enum: Object.values(VideoTypeEnum) },
      { enum: Object.values(AudioTypeEnum) },
    ],
  })
  @IsNotEmpty({ message: 'Loại media không được để trống' })
  @IsString({ message: 'Loại media phải là chuỗi' })
  mediaType: string;

  @ApiProperty({
    description: 'Kích thước tối đa của file (bytes)',
    enum: FileSizeEnum,
    example: FileSizeEnum.ONE_MB,
  })
  @IsNotEmpty({ message: 'Kích thước tối đa không được để trống' })
  @IsEnum(FileSizeEnum, { message: 'Kích thước tối đa không hợp lệ' })
  @Type(() => Number)
  fileSize: FileSizeEnum;

  @ApiProperty({
    description: 'Tên file (tùy chọn)',
    example: 'my-image.jpg',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên file phải là chuỗi' })
  fileName?: string;
}

/**
 * DTO cho response khi tạo URL tạm thời
 */
export class PresignedUrlResponseDto {
  @ApiProperty({
    description: 'URL tạm thời để upload file',
    example: 'https://storage.example.com/upload?token=abc123...',
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'Key của file trên storage',
    example: 'media/images/2023/04/12/my-image-1681289012345.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của URL (timestamp)',
    example: 1681289912345,
  })
  expiresAt: number;
}
